import type { Product, Category, User, Review } from "./types"

export const mockCategories: Category[] = [
  {
    id: "1",
    name: "Electronics",
    slug: "electronics",
    description: "Latest gadgets and electronic devices",
    image: "/electronics-category.png",
  },
  {
    id: "2",
    name: "Fashion",
    slug: "fashion",
    description: "Trendy clothing and accessories",
    image: "/fashion-category.png",
  },
  {
    id: "3",
    name: "Home & Garden",
    slug: "home-garden",
    description: "Everything for your home and garden",
    image: "/home-garden-category.png",
  },
  {
    id: "4",
    name: "Beauty",
    slug: "beauty",
    description: "Heat & humidity friendly beauty products",
    image: "/beauty-category.png",
  },
  {
    id: "5",
    name: "Education",
    slug: "education",
    description: "KCPE & CBC aligned educational materials",
    image: "/education-category.png",
  },
  {
    id: "6",
    name: "Technology",
    slug: "technology",
    description: "Digital goods and tech solutions",
    image: "/technology-category.png",
  },
  {
    id: "7",
    name: "Furniture",
    slug: "furniture",
    description: "Rental-friendly, flat-pack furniture",
    image: "/furniture-category.png",
  },
]

export const mockProducts: Product[] = [
  {
    id: "1",
    name: "Wireless Headphones Pro",
    slug: "wireless-headphones-pro",
    description: "Premium wireless headphones with noise cancellation and superior sound quality.",
    price: 299.99,
    salePrice: 249.99,
    compareAtPrice: 399.99,
    rating: 4.8,
    images: ["/black-wireless-headphones.png", "/wireless-headphones-side.png", "/wireless-headphones-case.png"],
    model3d: "/models/headphones.glb",
    categoryId: "1",
    category: mockCategories[0],
    inventory: 50,
    sku: "WHP-001",
    weight: 0.3,
    dimensions: { length: 20, width: 18, height: 8 },
    tags: ["wireless", "audio", "premium", "noise-cancelling"],
    featured: true,
    status: "active",
    createdAt: new Date("2024-01-15"),
    updatedAt: new Date("2024-01-15"),
  },
  {
    id: "2",
    name: "Smart Watch Series X",
    slug: "smart-watch-series-x",
    description: "Advanced smartwatch with health monitoring, GPS, and long battery life.",
    price: 449.99,
    rating: 4.6,
    images: ["/placeholder-h6szb.png", "/placeholder-5f6vv.png", "/placeholder-h2gu6.png"],
    model3d: "/models/smartwatch.glb",
    categoryId: "1",
    category: mockCategories[0],
    inventory: 30,
    sku: "SWX-001",
    weight: 0.05,
    dimensions: { length: 4, width: 4, height: 1 },
    tags: ["smartwatch", "fitness", "health", "gps"],
    featured: true,
    status: "active",
    createdAt: new Date("2024-01-10"),
    updatedAt: new Date("2024-01-10"),
  },
  {
    id: "3",
    name: "Designer Leather Jacket",
    slug: "designer-leather-jacket",
    description: "Premium leather jacket with modern cut and exceptional craftsmanship.",
    price: 599.99,
    salePrice: 479.99,
    compareAtPrice: 799.99,
    rating: 4.9,
    images: ["/placeholder-5g3id.png", "/placeholder-wsigt.png", "/leather-jacket-detail.png"],
    categoryId: "2",
    category: mockCategories[1],
    inventory: 15,
    sku: "LJ-001",
    weight: 1.2,
    dimensions: { length: 60, width: 50, height: 5 },
    tags: ["leather", "jacket", "fashion", "premium"],
    featured: true,
    status: "active",
    createdAt: new Date("2024-01-12"),
    updatedAt: new Date("2024-01-12"),
  },
  {
    id: "4",
    name: "Modern Coffee Table",
    slug: "modern-coffee-table",
    description: "Sleek modern coffee table with glass top and wooden base.",
    price: 399.99,
    rating: 4.3,
    images: ["/modern-coffee-table-glass.jpg", "/coffee-table-wooden-base.jpg", "/coffee-table-living-room.jpg"],
    model3d: "/models/coffee-table.glb",
    categoryId: "3",
    category: mockCategories[2],
    inventory: 8,
    sku: "CT-001",
    weight: 25,
    dimensions: { length: 120, width: 60, height: 45 },
    tags: ["furniture", "table", "modern", "glass"],
    featured: false,
    status: "active",
    createdAt: new Date("2024-01-08"),
    updatedAt: new Date("2024-01-08"),
  },
  // ELECTRONICS - KSh 1,500–12,000 sweet spot
  {
    id: "5",
    name: "20W USB-C + Lightning Fast-Charge Adapter",
    slug: "20w-usb-c-lightning-fast-charge-adapter",
    description: "20 W USB-C + Lightning fast-charge adapter (Safaricom & Airtel bundles now ship without bricks)",
    price: 2500,
    salePrice: 2200,
    rating: 4.7,
    images: ["/placeholder.svg", "/placeholder.svg"],
    categoryId: "1",
    category: mockCategories[0],
    inventory: 150,
    sku: "ELC-001",
    weight: 0.1,
    dimensions: { length: 6, width: 6, height: 3 },
    tags: ["charger", "usb-c", "lightning", "fast-charge", "safaricom", "airtel"],
    featured: true,
    status: "active",
    createdAt: new Date("2024-01-15"),
    updatedAt: new Date("2024-01-15"),
  },
  {
    id: "6",
    name: "5,000 mAh Solar Power Bank",
    slug: "5000-mah-solar-power-bank",
    description: "5,000 mAh power bank with solar strip – popular up-country where black-outs persist",
    price: 3500,
    salePrice: 3200,
    rating: 4.6,
    images: ["/placeholder.svg", "/placeholder.svg"],
    categoryId: "1",
    category: mockCategories[0],
    inventory: 80,
    sku: "ELC-002",
    weight: 0.3,
    dimensions: { length: 15, width: 8, height: 2 },
    tags: ["power-bank", "solar", "portable", "backup", "blackout"],
    featured: true,
    status: "active",
    createdAt: new Date("2024-01-16"),
    updatedAt: new Date("2024-01-16"),
  },
  {
    id: "7",
    name: "Bluetooth 5.3 Earbuds with Find-My",
    slug: "bluetooth-53-earbuds-find-my",
    description: "Bluetooth 5.3 ear-buds with \"Find-my\" beep (Jumia top-seller Q2-2025)",
    price: 4500,
    salePrice: 4200,
    rating: 4.8,
    images: ["/placeholder.svg", "/placeholder.svg"],
    categoryId: "1",
    category: mockCategories[0],
    inventory: 120,
    sku: "ELC-003",
    weight: 0.05,
    dimensions: { length: 8, width: 6, height: 3 },
    tags: ["bluetooth", "earbuds", "wireless", "find-my", "jumia-bestseller"],
    featured: true,
    status: "active",
    createdAt: new Date("2024-01-17"),
    updatedAt: new Date("2024-01-17"),
  },
  {
    id: "8",
    name: "3-in-1 MagSafe Charging Pad",
    slug: "3-in-1-magsafe-charging-pad",
    description: "3-in-1 MagSafe charging pad (Safaricom 5G users want tidy desks)",
    price: 8500,
    salePrice: 7800,
    rating: 4.5,
    images: ["/placeholder.svg", "/placeholder.svg"],
    categoryId: "1",
    category: mockCategories[0],
    inventory: 45,
    sku: "ELC-004",
    weight: 0.4,
    dimensions: { length: 20, width: 15, height: 2 },
    tags: ["magsafe", "wireless-charging", "3-in-1", "desk", "safaricom-5g"],
    featured: true,
    status: "active",
    createdAt: new Date("2024-01-18"),
    updatedAt: new Date("2024-01-18"),
  },
  {
    id: "9",
    name: "LED Rechargeable Desk Lamp",
    slug: "led-rechargeable-desk-lamp",
    description: "LED rechargeable desk lamp with 4-hour back-up (targets KCSE & campus students)",
    price: 3200,
    salePrice: 2900,
    rating: 4.4,
    images: ["/placeholder.svg", "/placeholder.svg"],
    categoryId: "1",
    category: mockCategories[0],
    inventory: 90,
    sku: "ELC-005",
    weight: 0.8,
    dimensions: { length: 40, width: 15, height: 50 },
    tags: ["led", "desk-lamp", "rechargeable", "students", "kcse", "campus"],
    featured: true,
    status: "active",
    createdAt: new Date("2024-01-19"),
    updatedAt: new Date("2024-01-19"),
  },
  // FASHION - Kitenge twist + streetwear
  {
    id: "10",
    name: "Unisex Kitenge Bomber Jacket",
    slug: "unisex-kitenge-bomber-jacket",
    description: "Unisex Kitenge bomber jacket (lightweight, KSh 2,800–3,500)",
    price: 3500,
    salePrice: 3200,
    rating: 4.6,
    images: ["/placeholder.svg", "/placeholder.svg"],
    categoryId: "2",
    category: mockCategories[1],
    inventory: 60,
    sku: "FSH-001",
    weight: 0.5,
    dimensions: { length: 70, width: 60, height: 5 },
    tags: ["kitenge", "bomber-jacket", "unisex", "lightweight", "african-print"],
    featured: true,
    status: "active",
    createdAt: new Date("2024-01-20"),
    updatedAt: new Date("2024-01-20"),
  },
  {
    id: "11",
    name: "High-Waist Mom Jeans",
    slug: "high-waist-mom-jeans",
    description: "High-waist \"mom\" jeans – washed grey, ripped, paired with chunky boots",
    price: 4200,
    salePrice: 3800,
    rating: 4.7,
    images: ["/placeholder.svg", "/placeholder.svg"],
    categoryId: "2",
    category: mockCategories[1],
    inventory: 75,
    sku: "FSH-002",
    weight: 0.6,
    dimensions: { length: 100, width: 40, height: 3 },
    tags: ["jeans", "high-waist", "mom-jeans", "ripped", "washed-grey"],
    featured: true,
    status: "active",
    createdAt: new Date("2024-01-21"),
    updatedAt: new Date("2024-01-21"),
  },
  {
    id: "12",
    name: "Cargo Utility Pants",
    slug: "cargo-utility-pants",
    description: "Cargo utility pants (cotton-drill, side pockets) – 2023 trend still moving units",
    price: 3800,
    salePrice: 3500,
    rating: 4.5,
    images: ["/placeholder.svg", "/placeholder.svg"],
    categoryId: "2",
    category: mockCategories[1],
    inventory: 85,
    sku: "FSH-003",
    weight: 0.7,
    dimensions: { length: 100, width: 45, height: 3 },
    tags: ["cargo-pants", "utility", "cotton-drill", "pockets", "streetwear"],
    featured: true,
    status: "active",
    createdAt: new Date("2024-01-22"),
    updatedAt: new Date("2024-01-22"),
  },
  {
    id: "13",
    name: "Ankara-Print Bucket Hat",
    slug: "ankara-print-bucket-hat",
    description: "Ankara-print bucket hat (foldable, UV-proof)",
    price: 1800,
    salePrice: 1600,
    rating: 4.4,
    images: ["/placeholder.svg", "/placeholder.svg"],
    categoryId: "2",
    category: mockCategories[1],
    inventory: 100,
    sku: "FSH-004",
    weight: 0.1,
    dimensions: { length: 30, width: 30, height: 15 },
    tags: ["ankara", "bucket-hat", "foldable", "uv-proof", "african-print"],
    featured: true,
    status: "active",
    createdAt: new Date("2024-01-23"),
    updatedAt: new Date("2024-01-23"),
  },
  {
    id: "14",
    name: "Chunky PVC Ankle Boots",
    slug: "chunky-pvc-ankle-boots",
    description: "Chunky PVC ankle boots (El-Niño proof, sells Nov–Feb)",
    price: 4500,
    salePrice: 4200,
    rating: 4.3,
    images: ["/placeholder.svg", "/placeholder.svg"],
    categoryId: "2",
    category: mockCategories[1],
    inventory: 50,
    sku: "FSH-005",
    weight: 1.2,
    dimensions: { length: 30, width: 12, height: 20 },
    tags: ["boots", "pvc", "chunky", "waterproof", "el-nino", "ankle-boots"],
    featured: true,
    status: "active",
    createdAt: new Date("2024-01-24"),
    updatedAt: new Date("2024-01-24"),
  },
  // BEAUTY - heat & humidity friendly
  {
    id: "15",
    name: "Shea-Butter & Coconut Hair Food",
    slug: "shea-butter-coconut-hair-food",
    description: "100 ml shea-butter & coconut hair food (sulfate-free) – staple for 4C hair",
    price: 1200,
    salePrice: 1000,
    rating: 4.8,
    images: ["/placeholder.svg", "/placeholder.svg"],
    categoryId: "4",
    category: mockCategories[3],
    inventory: 200,
    sku: "BTY-001",
    weight: 0.12,
    dimensions: { length: 8, width: 8, height: 10 },
    tags: ["shea-butter", "coconut", "hair-food", "sulfate-free", "4c-hair"],
    featured: true,
    status: "active",
    createdAt: new Date("2024-01-25"),
    updatedAt: new Date("2024-01-25"),
  },
  {
    id: "16",
    name: "SPF-50 Sunscreen Lotion",
    slug: "spf-50-sunscreen-lotion",
    description: "SPF-50 sunscreen lotion (50 ml) – growing awareness, Safaricom IPO adverts pushed skin-cancer stats",
    price: 1800,
    salePrice: 1600,
    rating: 4.6,
    images: ["/placeholder.svg", "/placeholder.svg"],
    categoryId: "4",
    category: mockCategories[3],
    inventory: 150,
    sku: "BTY-002",
    weight: 0.06,
    dimensions: { length: 6, width: 4, height: 12 },
    tags: ["sunscreen", "spf-50", "skin-protection", "safaricom", "awareness"],
    featured: true,
    status: "active",
    createdAt: new Date("2024-01-26"),
    updatedAt: new Date("2024-01-26"),
  },
  {
    id: "17",
    name: "Vitamin-C + Turmeric Face Serum",
    slug: "vitamin-c-turmeric-face-serum",
    description: "Vitamin-C + turmeric face serum (dark bottle) – hyper-pigmentation is top search query",
    price: 2200,
    salePrice: 2000,
    rating: 4.7,
    images: ["/placeholder.svg", "/placeholder.svg"],
    categoryId: "4",
    category: mockCategories[3],
    inventory: 120,
    sku: "BTY-003",
    weight: 0.05,
    dimensions: { length: 5, width: 5, height: 10 },
    tags: ["vitamin-c", "turmeric", "face-serum", "hyperpigmentation", "dark-bottle"],
    featured: true,
    status: "active",
    createdAt: new Date("2024-01-27"),
    updatedAt: new Date("2024-01-27"),
  },
  {
    id: "18",
    name: "Collagen Under-Eye Masks",
    slug: "collagen-under-eye-masks",
    description: "Collagen under-eye masks (30 pairs) – TikTok trend in Nairobi salons",
    price: 2800,
    salePrice: 2500,
    rating: 4.5,
    images: ["/placeholder.svg", "/placeholder.svg"],
    categoryId: "4",
    category: mockCategories[3],
    inventory: 80,
    sku: "BTY-004",
    weight: 0.15,
    dimensions: { length: 12, width: 8, height: 3 },
    tags: ["collagen", "under-eye", "masks", "tiktok", "nairobi-salons"],
    featured: true,
    status: "active",
    createdAt: new Date("2024-01-28"),
    updatedAt: new Date("2024-01-28"),
  },
  {
    id: "19",
    name: "Refillable Perfume Atomiser",
    slug: "refillable-perfume-atomiser",
    description: "Refillable 60 ml perfume atomiser (airport-compliant) – domestic travel rebound",
    price: 1500,
    salePrice: 1300,
    rating: 4.4,
    images: ["/placeholder.svg", "/placeholder.svg"],
    categoryId: "4",
    category: mockCategories[3],
    inventory: 100,
    sku: "BTY-005",
    weight: 0.08,
    dimensions: { length: 10, width: 3, height: 15 },
    tags: ["perfume", "atomiser", "refillable", "airport-compliant", "travel"],
    featured: true,
    status: "active",
    createdAt: new Date("2024-01-29"),
    updatedAt: new Date("2024-01-29"),
  },
  // EDUCATION - KCPE & CBC aligned
  {
    id: "20",
    name: "Grade 6 CBC Approved Atlas",
    slug: "grade-6-cbc-approved-atlas",
    description: "Grade 6 CBC approved atlas (Moran Publishers) – new curriculum still short on copies",
    price: 1800,
    salePrice: 1600,
    rating: 4.6,
    images: ["/placeholder.svg", "/placeholder.svg"],
    categoryId: "5",
    category: mockCategories[4],
    inventory: 120,
    sku: "EDU-001",
    weight: 0.8,
    dimensions: { length: 30, width: 21, height: 2 },
    tags: ["atlas", "cbc", "grade-6", "moran-publishers", "curriculum"],
    featured: true,
    status: "active",
    createdAt: new Date("2024-01-30"),
    updatedAt: new Date("2024-01-30"),
  },
  {
    id: "21",
    name: "A4 Magnetic White-Board + 4 Markers",
    slug: "a4-magnetic-whiteboard-4-markers",
    description: "A4 magnetic white-board + 4 markers – home-schooling in Eastlands estates",
    price: 2500,
    salePrice: 2200,
    rating: 4.5,
    images: ["/placeholder.svg", "/placeholder.svg"],
    categoryId: "5",
    category: mockCategories[4],
    inventory: 90,
    sku: "EDU-002",
    weight: 0.6,
    dimensions: { length: 30, width: 21, height: 1 },
    tags: ["whiteboard", "magnetic", "markers", "homeschooling", "eastlands"],
    featured: true,
    status: "active",
    createdAt: new Date("2024-01-31"),
    updatedAt: new Date("2024-01-31"),
  },
  {
    id: "22",
    name: "Scientific Calculator FX-82MS",
    slug: "scientific-calculator-fx-82ms",
    description: "Scientific calculator FX-82MS (KNEC approved) – must-have for KCSE",
    price: 3200,
    salePrice: 2900,
    rating: 4.8,
    images: ["/placeholder.svg", "/placeholder.svg"],
    categoryId: "5",
    category: mockCategories[4],
    inventory: 150,
    sku: "EDU-003",
    weight: 0.2,
    dimensions: { length: 16, width: 8, height: 2 },
    tags: ["calculator", "scientific", "fx-82ms", "knec", "kcse"],
    featured: true,
    status: "active",
    createdAt: new Date("2024-02-01"),
    updatedAt: new Date("2024-02-01"),
  },
  {
    id: "23",
    name: "Re-usable ABC Tracing Book",
    slug: "reusable-abc-tracing-book",
    description: "Re-usable ABC tracing book (water-wipe marker) – middle-class parents avoiding print-costs",
    price: 1200,
    salePrice: 1000,
    rating: 4.4,
    images: ["/placeholder.svg", "/placeholder.svg"],
    categoryId: "5",
    category: mockCategories[4],
    inventory: 200,
    sku: "EDU-004",
    weight: 0.3,
    dimensions: { length: 25, width: 20, height: 1 },
    tags: ["abc", "tracing", "reusable", "water-wipe", "middle-class"],
    featured: true,
    status: "active",
    createdAt: new Date("2024-02-02"),
    updatedAt: new Date("2024-02-02"),
  },
  {
    id: "24",
    name: "32 GB USB 3.0 Flash Disk",
    slug: "32gb-usb-30-flash-disk",
    description: "32 GB USB 3.0 flash disk – students move CAT files across cyber-cafés",
    price: 1800,
    salePrice: 1600,
    rating: 4.6,
    images: ["/placeholder.svg", "/placeholder.svg"],
    categoryId: "5",
    category: mockCategories[4],
    inventory: 180,
    sku: "EDU-005",
    weight: 0.02,
    dimensions: { length: 6, width: 2, height: 1 },
    tags: ["usb", "flash-disk", "32gb", "students", "cyber-cafe"],
    featured: true,
    status: "active",
    createdAt: new Date("2024-02-03"),
    updatedAt: new Date("2024-02-03"),
  },
  // TECHNOLOGY - non-electronic, digital goods
  {
    id: "25",
    name: "Farming-as-a-Service Notion Template",
    slug: "farming-as-service-notion-template",
    description: "\"Farming-as-a-Service\" Notion template (record inputs, yields, profits) – agri-Tech boom",
    price: 800,
    salePrice: 600,
    rating: 4.3,
    images: ["/placeholder.svg", "/placeholder.svg"],
    categoryId: "6",
    category: mockCategories[5],
    inventory: 999,
    sku: "TECH-001",
    weight: 0,
    dimensions: { length: 0, width: 0, height: 0 },
    tags: ["notion", "template", "farming", "agri-tech", "digital"],
    featured: true,
    status: "active",
    createdAt: new Date("2024-02-04"),
    updatedAt: new Date("2024-02-04"),
  },
  {
    id: "26",
    name: "Canva Swahili-Quote Social Template Pack",
    slug: "canva-swahili-quote-social-template-pack",
    description: "Canva Swahili-quote social template pack (120 posts) – SMEs moving online",
    price: 1200,
    salePrice: 1000,
    rating: 4.5,
    images: ["/placeholder.svg", "/placeholder.svg"],
    categoryId: "6",
    category: mockCategories[5],
    inventory: 999,
    sku: "TECH-002",
    weight: 0,
    dimensions: { length: 0, width: 0, height: 0 },
    tags: ["canva", "swahili", "social-media", "templates", "sme"],
    featured: true,
    status: "active",
    createdAt: new Date("2024-02-05"),
    updatedAt: new Date("2024-02-05"),
  },
  {
    id: "27",
    name: "Start a Boda-Boda Spare-Parts Biz E-book",
    slug: "start-boda-boda-spare-parts-biz-ebook",
    description: "E-book bundle: \"Start a Boda-Boda Spare-Parts Biz\" – youth unemployment niche",
    price: 1500,
    salePrice: 1200,
    rating: 4.4,
    images: ["/placeholder.svg", "/placeholder.svg"],
    categoryId: "6",
    category: mockCategories[5],
    inventory: 999,
    sku: "TECH-003",
    weight: 0,
    dimensions: { length: 0, width: 0, height: 0 },
    tags: ["ebook", "boda-boda", "spare-parts", "business", "youth"],
    featured: true,
    status: "active",
    createdAt: new Date("2024-02-06"),
    updatedAt: new Date("2024-02-06"),
  },
  {
    id: "28",
    name: "Printable Monthly Budget Sheet (KES)",
    slug: "printable-monthly-budget-sheet-kes",
    description: "Printable monthly budget sheet (KES version) – chama groups",
    price: 500,
    salePrice: 400,
    rating: 4.2,
    images: ["/placeholder.svg", "/placeholder.svg"],
    categoryId: "6",
    category: mockCategories[5],
    inventory: 999,
    sku: "TECH-004",
    weight: 0,
    dimensions: { length: 0, width: 0, height: 0 },
    tags: ["budget", "printable", "kes", "chama", "financial"],
    featured: true,
    status: "active",
    createdAt: new Date("2024-02-07"),
    updatedAt: new Date("2024-02-07"),
  },
  {
    id: "29",
    name: "Royalty-Free Gengetone Loop Pack",
    slug: "royalty-free-gengetone-loop-pack",
    description: "Royalty-free gengetone loop pack – YouTube & TikTok creators",
    price: 2000,
    salePrice: 1800,
    rating: 4.6,
    images: ["/placeholder.svg", "/placeholder.svg"],
    categoryId: "6",
    category: mockCategories[5],
    inventory: 999,
    sku: "TECH-005",
    weight: 0,
    dimensions: { length: 0, width: 0, height: 0 },
    tags: ["gengetone", "loops", "royalty-free", "youtube", "tiktok"],
    featured: true,
    status: "active",
    createdAt: new Date("2024-02-08"),
    updatedAt: new Date("2024-02-08"),
  },
  // FURNITURE - rental-friendly, flat-pack
  {
    id: "30",
    name: "Bamboo Foldable Laptop Bed-Tray",
    slug: "bamboo-foldable-laptop-bed-tray",
    description: "Bamboo foldable laptop bed-tray (saves space in 1-bed SQ apartments)",
    price: 2800,
    salePrice: 2500,
    rating: 4.4,
    images: ["/placeholder.svg", "/placeholder.svg"],
    categoryId: "7",
    category: mockCategories[6],
    inventory: 70,
    sku: "FUR-001",
    weight: 1.2,
    dimensions: { length: 60, width: 35, height: 5 },
    tags: ["bamboo", "laptop-tray", "foldable", "space-saving", "apartment"],
    featured: true,
    status: "active",
    createdAt: new Date("2024-02-09"),
    updatedAt: new Date("2024-02-09"),
  },
  {
    id: "31",
    name: "Velvet Ottoman with Hidden Storage",
    slug: "velvet-ottoman-hidden-storage",
    description: "Velvet ottoman with hidden storage (arrives flat, 3 kg)",
    price: 4500,
    salePrice: 4200,
    rating: 4.5,
    images: ["/placeholder.svg", "/placeholder.svg"],
    categoryId: "7",
    category: mockCategories[6],
    inventory: 40,
    sku: "FUR-002",
    weight: 3,
    dimensions: { length: 40, width: 40, height: 40 },
    tags: ["ottoman", "velvet", "storage", "flat-pack", "furniture"],
    featured: true,
    status: "active",
    createdAt: new Date("2024-02-10"),
    updatedAt: new Date("2024-02-10"),
  },
  {
    id: "32",
    name: "Iron & Mesh Shoe Rack (5-Tier)",
    slug: "iron-mesh-shoe-rack-5-tier",
    description: "Iron & mesh shoe rack (5-tier, tool-free assembly)",
    price: 3200,
    salePrice: 2900,
    rating: 4.3,
    images: ["/placeholder.svg", "/placeholder.svg"],
    categoryId: "7",
    category: mockCategories[6],
    inventory: 60,
    sku: "FUR-003",
    weight: 4,
    dimensions: { length: 60, width: 30, height: 150 },
    tags: ["shoe-rack", "iron", "mesh", "5-tier", "tool-free"],
    featured: true,
    status: "active",
    createdAt: new Date("2024-02-11"),
    updatedAt: new Date("2024-02-11"),
  },
  {
    id: "33",
    name: "Height-Adjustable Plastic Side Table",
    slug: "height-adjustable-plastic-side-table",
    description: "Height-adjustable plastic side table (outdoor balcony friendly)",
    price: 2200,
    salePrice: 2000,
    rating: 4.2,
    images: ["/placeholder.svg", "/placeholder.svg"],
    categoryId: "7",
    category: mockCategories[6],
    inventory: 80,
    sku: "FUR-004",
    weight: 2,
    dimensions: { length: 40, width: 40, height: 60 },
    tags: ["side-table", "adjustable", "plastic", "outdoor", "balcony"],
    featured: true,
    status: "active",
    createdAt: new Date("2024-02-12"),
    updatedAt: new Date("2024-02-12"),
  },
  {
    id: "34",
    name: "Stackable Mono-Block Garden Chair",
    slug: "stackable-mono-block-garden-chair",
    description: "Stackable mono-block garden chair (UV-stabilised, sells to Air-BnB hosts at Coast)",
    price: 1800,
    salePrice: 1600,
    rating: 4.1,
    images: ["/placeholder.svg", "/placeholder.svg"],
    categoryId: "7",
    category: mockCategories[6],
    inventory: 120,
    sku: "FUR-005",
    weight: 1.5,
    dimensions: { length: 50, width: 50, height: 80 },
    tags: ["chair", "stackable", "mono-block", "uv-stabilised", "airbnb"],
    featured: true,
    status: "active",
    createdAt: new Date("2024-02-13"),
    updatedAt: new Date("2024-02-13"),
  },
]

export const mockUsers: User[] = [
  {
    id: "1",
    email: "<EMAIL>",
    name: "Admin User",
    role: "admin",
    createdAt: new Date("2024-01-01"),
    updatedAt: new Date("2024-01-01"),
  },
  {
    id: "2",
    email: "<EMAIL>",
    name: "John Doe",
    role: "customer",
    createdAt: new Date("2024-01-05"),
    updatedAt: new Date("2024-01-05"),
  },
]

export const mockReviews: Review[] = [
  {
    id: "1",
    productId: "1",
    userId: "2",
    user: mockUsers[1],
    rating: 5,
    title: "Amazing sound quality!",
    content: "These headphones exceeded my expectations. The noise cancellation is incredible.",
    verified: true,
    createdAt: new Date("2024-01-20"),
  },
  {
    id: "2",
    productId: "2",
    userId: "2",
    user: mockUsers[1],
    rating: 4,
    title: "Great smartwatch",
    content: "Love the health features and battery life. Very comfortable to wear.",
    verified: true,
    createdAt: new Date("2024-01-18"),
  },
]
